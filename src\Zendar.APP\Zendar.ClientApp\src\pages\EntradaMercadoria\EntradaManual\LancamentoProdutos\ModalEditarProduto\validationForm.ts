import { yupResolver as yupResolverInstance } from '@hookform/resolvers/yup';
import * as yup from 'yup';

import ConstanteMensagemValidacao from 'constants/mensagensValidacoes';

import {
  Cor,
  TamanhoQuantidade,
} from '../ModalAdicionarProduto/validationForm';

export type FormData = {
  quantidade: number;
  valorUnitario: number;
  ipi: number;
  icmsSt: number;
  fcpSt: number;
  custoAdicional: number;
  cor: Cor | null;
  listaTamanhoIdQuantidade: TamanhoQuantidade[];
};

const schema = yup.object().shape({
  quantidade: yup
    .number()
    .moreThan(0, ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .typeError(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
  valorUnitario: yup.number().nullable(),
  ipi: yup.number().nullable(),
  icmsSt: yup.number().nullable(),
  fcpSt: yup.number().nullable(),
  custoAdicional: yup.number().nullable(),
});

export const yupResolver = yupResolverInstance(schema);
